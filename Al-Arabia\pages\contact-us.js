import React, { useState, useEffect,   } from "react";
import <PERSON> from "next/head";
import HeadMarquee from "@/component/HeadMarquee";
import ContactSection from "@/component/ContactSection";
import comon from "@/styles/comon.module.scss";
import parse from 'html-react-parser';
import { fetchPageById, fetchPostList } from '@/lib/api/pageApi';
import ContactSec from "@/component/ContactSec";

export default function Home({pageData}) {

	return (
		<>
			 
			<HeadMarquee />
			<section className={`${comon.no_banner}`}>
				<div className={`${comon.wrap}`}>
					<ContactSec
						title={pageData.acf.title}
						paragraph={pageData.acf.short_description}
						button="Submit"
						showAutoMargin={true}
					/>
				</div>
			</section>
		</>
	);
}


export async function getStaticProps({locale}) {
	const langCode = locale === "ar" ? "ar" : "en";
	//const langCode = "en"
	try {
	  const pageData = await fetchPageById("contact", langCode);  
	  return {
		props: {
			pageData, 
		},
		revalidate: 10, 
	  };
	} catch (error) {
	  console.error('Error fetching data:', error);
	  return {
		props: {
			pageData: null, 
		},
	  };
	}
  }