@import "variable", "base", "mixin";

.footer {
  background: linear-gradient(107.56deg,
      rgba(103, 90, 167, 1) 0%,
      rgba(58, 48, 100, 1) 100%);
  color: #fff;

  .footer_cl_01 {
    width: 46.3%;
    padding-right: 3%;

    @media #{$media-820} {
      width: 42%;
    }

    @media #{$media-700} {
      width: 100%;
      border-bottom: solid 1px #64559b;
      padding-bottom: 25px;
      // margin-bottom: 25px;
      padding-right: 0;
    }
  }

  .footer_cl_02 {
    width: 53.7%;
    margin-top: 57px;

    @media #{$media-820} {
      width: 58%;
    }

    @media #{$media-700} {
      width: 100%;
      margin-top: 0;
      padding-bottom: 20px;
    }
  }

  p {
    color: #ffffff;
    font-family: var(--aller_lt);
    font-size: 14px;

    @media #{$media-700} {
      font-size: 13px;
      line-height: 16px;
    }
  }

  .social_ul {
    margin: 0 -3%;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    margin-top: 30px;

    li {
      list-style: none;
      padding: 0 3%;

      a {
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #4c3d83;
        }
      }
    }

    @media #{$media-700} {
      margin-top: 20px;
    }
  }

  .footer_ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;

    li {
      list-style: none;

      h5 {
        // @include rem(15);
        font-size: 14px;

        font-family: var(--aller_lt);
        margin-bottom: 10px;
        font-weight: 700;

        @media #{$media-700} {
          margin-bottom: 8px;
          font-size: 16px;
        }

        @media #{$media-500} {
          font-size: 15px;
        }
      }

      a {
        font-size: 12px;
        font-family: var(--aller_lt);

        @media #{$media-700} {
          font-size: 13px;
        }

        &:hover {
          color: #b6a1ff;
        }
      }

      .footer_link {
        li {
          margin-bottom: 8px;

          @media #{$media-700} {
            margin-bottom: 7px;
          }
        }
      }

      p {
        line-height: 25px;
        font-size: 12px;

        @media #{$media-700} {
          font-size: 13px;
        }
      }
    }

    .footer_link_cl_01 {
      width: 36%;

      @media #{$media-820} {
        width: 26%;
      }

      @media #{$media-700} {
        width: 50%;
      }
    }

    .footer_link_cl_02 {
      width: 33%;

      @media #{$media-700} {
        width: 50%;
      }
    }

    .footer_link_cl_03 {
      width: 31%;

      @media #{$media-820} {
        width: 41%;
      }

      @media #{$media-700} {
        width: 100%;
        // border-top: solid 1px #64559b;
        // padding-top: 25px;
        padding-top: 15px;
        // margin-top: 25px;
      }
    }
  }

  .copy_block {
    border-top: solid 1px rgba($color: #ffffff, $alpha: 0.15);
    margin-top: 25px;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .footer_bot_link_ul_outer {
    .footer_bot_link_ul {
      display: flex;
      flex-wrap: wrap;
      margin: 0;

      li {
        padding-left: 20px;
        padding-right: 20px;

        a {
          color: #fff;
          font-family: var(--aller_lt);

          &:hover {
            color: #ccbeff;
          }

          @media #{$media-768} {
            font-size: 13px;
          }
        }

        @media #{$media-768} {
          padding-left: 3%;
          padding-right: 3%;
        }
      }

      @media #{$media-768} {
        margin: 0;
      }
    }
  }

  .footer_bot_link_outer {
    border-top: solid 1px rgba($color: #fff, $alpha: 0.15);
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 15px;

    .footer_bot_link_outer_bot {
      width: 42%;
      justify-content: flex-end;
      display: flex;
      flex-wrap: wrap;

      .footer_bot_link_ul {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        li {
          width: 33%;
          text-align: right;

          a {
            color: #e0e0e0;

            font-size: 12px;
            font-family: var(--aller_lt);
            opacity: 0.8;
            text-transform: none;

            @media #{$media-700} {
              font-size: 14px;
            }

            &:hover {
              color: #ccbeff;
            }
          }

          &:nth-child(2) {
            padding-right: 2%;

            @include max(1024) {
              padding-right: 0%;
            }
          }

          &:last-child {
            padding-right: 5%;

            @include max(1024) {
              padding-right: 0px;
            }
          }

          @media #{$media-700} {
            width: auto;
            padding-bottom: 7px;
            padding-left: 10px;
            padding-right: 10px;
          }
        }

        @media #{$media-700} {
          justify-content: center;
          padding-top: 5px;
        }
      }

      @media #{$media-700} {
        width: 100%;
      }
    }

    .col_rew {
      flex-direction: column-reverse;
    }

    @media #{$media-700} {
      display: none;
    }
  }
}

.subscribe_ul {
  margin: 0;
  padding: 0;
  display: flex;
  // width: 69%;
  width: 72%;

  li {
    list-style: none;

    &.sub_block_01 {
      width: calc(100% - 77px);
    }

    &.sub_block_02 {
      width: 77px;
    }

    .email_fld {
      font-size: 14px;
      // font-size: 12px;
      background: transparent;
      color: #fff;
      border: none;
      border-radius: 0px;
      outline: none;
      width: 100%;
      font-family: var(--aller_lt);

      border-bottom: solid 1px #fff;
      height: 33px;

      &::placeholder {
        font-size: 11px;


      }

      @media #{$media-500} {
        font-size: 16px;
      }
    }

    .sub_buttion {
      width: 100%;
      background: transparent;
      outline: none;
      border: solid 1px #ffffff;
      height: 33px;
      color: #fff;
      cursor: pointer;
      border-radius: 0;
      font-family: inherit;
      
      &:hover {
        background: #64559b;
      }
    }
  }

  ::-webkit-input-placeholder {
    /* Chrome/Opera/Safari */
    color: #fff;
  }

  ::-moz-placeholder {
    /* Firefox 19+ */
    color: #fff;
  }

  :-ms-input-placeholder {
    /* IE 10+ */
    color: #fff;
  }

  :-moz-placeholder {
    /* Firefox 18- */
    color: #fff;
  }

  @media #{$media-820} {
    width: 100%;
  }

  @media #{$media-700} {
    width: 100%;
  }
}

:global(body.rtl) .email_fld {
  font-family: var(--arbfonts);
}

.footer_bot_copy_block {
  width: 25%;

  p {
    font-size: 12px;
    color: white;
  }

  @media #{$media-820} {
    width: max-content;
  }

  @media #{$media-700} {
    width: 100%;
    text-align: center;
  }
}

.mobile_hide {
  @media #{$media-700} {
    display: none;
  }
}

.subscribe_block {
  >p {
    font-size: 11px;
  }

  @media #{$media-700} {
    display: none;
  }
}

.accordion_block_li {
  .accordion_block {
    @media #{$media-700} {
      margin: 10px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // background-color: rgba(221, 221, 221, 0.253);
    }
  }

  .accordion_icon {
    width: 15px;
    height: auto;
    display: none;
    margin-right: 7px;
    transition: all 0.3s ease-in-out;

    img {
      height: auto;
      width: 100%;
      display: block;
      object-fit: contain;
    }

    @media #{$media-700} {
      display: block;
    }

    &.flip_icon {
      transform: scaleY(-1);
    }
  }

  .accordion_show {
    @media #{$media-700} {
      display: block;
      padding-bottom: 10px;
    }
  }

  .accordion_hide {
    @media #{$media-700} {
      display: none;
    }
  }

  @media #{$media-700} {
    display: none;
    border-bottom: solid 1px #64559b;

    width: 100% !important;
  }
}

.validation_msg {
  font-size: 14px !important;
  line-height: 150%;

  @media #{$media-768} {
    font-size: 12px !important;
  }
}

:global(body.rtl) {
  .subscribe_ul {
    li {
      .sub_buttion {
        font-family: var(--arbfonts) !important;
      }
    }
  }
}