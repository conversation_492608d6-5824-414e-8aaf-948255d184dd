@import "variable", "base";
// @import url('https://fonts.cdnfonts.com/css/aller');
@import url('https://fonts.cdnfonts.com/css/aller?styles=27,22');



.header {
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
  position: absolute;
  width: 100%;
  left: 0;
  top: 50px;
  z-index: 100;
}



.marqui {
  background: #5e45ff;
}

.marquee {
  padding-top: 15px;
  padding-bottom: 15px;
  background: #6758b6;
  color: #fff;
  font-size: 16px;
  white-space: nowrap;
  font-weight: 400;
  line-height: 20px;
}

.marquee_item {
  border-right: solid 1px #fff !important;
  padding-left: 15px;
  padding-right: 15px;

  p {
    color: $white;

    span {
      font-family: var(--segoeUiSemibold);
    }
  }
}

.about_hero__marquee_row {
  display: flex;
  position: relative;
  white-space: nowrap;

  .marq_block {}
}

.logo_block {
  width: 11.9%;
}

.menu_block {
  width: 80%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  position: relative;

  .menu_ul {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -20px;
    padding-left: 5%;
    padding-right: 5%;

    li {
      padding: 0% 20px;

      a {
        color: #fff;

        &:hover {
          color: #c1b0ff;
        }
      }
    }
  }

  .lang_switch {
    background: #6758b6;
    display: flex;
    padding: 5px 10px;
    color: #fff;
    align-items: center;

    &:hover {
      background: #5647a0;
    }
  }
}

.mobile_menu_sub_list {
  @media #{$media-1024} {

    // transform: translateX(10px);
  }

  .submenu_list {
    @media #{$media-1024} {

      // transform: translateX(-10px);
      text-align: left;
      align-items: start;
      position: absolute;
      z-index: 800;
      right: 0;
      left: 50%;
      // top: 10px;
      top: 0px;

    }

  }
}


.header_main {
  position: absolute;
  // top: 45px;
  width: 100%;
  left: 0;
  padding-top: 50px;
  padding-bottom: 30px;
  z-index: 900;
  z-index: 1200;
  transition: all 0.3s ease;

  &.header_top_home {
    top: 0px !important;

  }

  &.sticky {
    position: fixed !important;
    top: 0 !important;
    left: 0;
    width: 100%;
    padding: 0;
    background: #fff;

    .menu_block_02 {
      background: transparent;
      border: none;

      .menu_ul_block {
        a {
          color: #3b3064;


          &.mob_active_nav_head {

            color: #a190f1 !important;

          }

          // @media #{$media-700} {
          @media #{$media-1024} {

            color: #ffffff !important;
          }
        }


      }




      @media #{$media-700} {
        width: 95%;
      }


    }

    .menu_ul_block {
      position: unset;

      &::after {
        border-radius: 0;
        top: 67px;
      }
    }

    .dropdown_menu {
      li {
        a {
          color: #fff !important;

          &:hover {
            color: #b6a7ff !important;
          }
        }
      }
    }
  }



  // @media #{$media-700} {
  @media #{$media-1024} {

    padding: 18px 0 0 0;
  }
}

.menu_block_02 {
  max-width: 733px;
  // width: 90%;
  width: 85%;
  margin-left: auto;
  margin-right: auto;
  background: rgba(81, 72, 135, 0.9);
  border-radius: 10px;
  border: 1px solid #6758b6;
  transition: all 0.3s ease;
  align-items: center;

  &.center_item {

    // @media #{$media-820} {
    @media #{$media-1024} {
      position: relative;
      align-items: center;
    }
  }

  // &.active {
  //   border-radius: 0px;
  //   width: 100%;

  // }

  // &.sticky {
  //   max-width: 100%;
  //   background: #ececec;
  //   width: 100%;
  //   border-radius: 0px;
  //   border: none;
  //   border-bottom: 1px solid #ececec;
  //   position: fixed;
  //   top: 0;
  //   z-index: 999;

  //   @media #{$media-700} {
  //     width: 100%;
  //   }

  //   .menu_ul_block {
  //     &::after{
  //       border-radius: 0;
  //       border: none;
  //     }
  //   }
  // }

  // @media #{$media-700} {
  @media #{$media-1024} {

    display: flex;
    padding: 17px 3%;
    justify-content: space-between;
    background: rgba(81, 72, 135, 1);

  }
}

.menu_ul_block {
  display: flex;
  justify-content: space-evenly;
  position: relative;
  width: 100%;

  &::after {
    content: "";
    width: 100%;
    // min-height: 260px;
    min-height: 285px;
    background: rgba(81, 72, 135, 0.9);
    border-radius: 17px;
    border: 1px solid #6758b6;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: all 0.3s ease-in;
    opacity: 0;
    visibility: hidden;
    backdrop-filter: blur(3px);

    @media #{$media-1024} {
      border-radius: 0 0 15px 15px;
      background: rgba(81, 72, 135, 1);
      top: -10px;
      left: unset;
      min-height: 50px;
    }
  }

  &.active {
    &::after {
      opacity: 1;
      visibility: visible;
    }
  }

  // gap:10%;

  >li {
    display: flex;
    align-items: center;
    position: relative;

    >a {
      display: block;
      // font-family: var(--aller_lt);
      font-family: 'Aller';

      padding: 23px 0;
      color: white;

      font-size: 17px;
      font-weight: 400;

      // &.dropdown_link {
      //   padding: 24px 0;
      //   font-family: var(--aller_lt);
      // }

      &.active_nav_head {
        color: #b6a7ff;
      }

      @media #{$media-820} {
        font-size: 15px;
      }

      >img {

        // @media #{$media-700} {
        @media #{$media-1024} {

          display: none;
        }
      }

      // @media #{$media-700} {
      @media #{$media-1024} {

        // font-size: 20px;
        // color: #fff !important;
      }

      // @media #{$media-700} {
      @media #{$media-1024} {

        padding: 9px 0;
        font-weight: 500 !important;
      }

      @media #{$media-700} {

        // font-size: 18px;
      }

      @media #{$media-500} {

        font-size: 15px;
      }
    }

    &:hover a {
      color: #b6a7ff;
    }

    .dropdown_arrow {
      height: 100%;
      width: 15px;
      display: flex;
      margin: 0 10px;
      align-items: center;

      @media #{$media-820} {
        width: 12px;
        position: relative;
        display: flex;
        margin: 0 10px;
      }

      img {
        transition: all 0.3s ease;
        display: block;
        object-fit: contain;
        height: auto;
        width: 100%;
      }

      &.active {
        img {

          // @media #{$media-700} {
          @media #{$media-1024} {

            transform: scaleY(-1) !important;
          }
        }
      }
    }

    .dropdown_menu {
      position: absolute;
      top: 98%;
      left: 0;
      min-width: 250px;
      padding: 0;
      padding-top: 25px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      opacity: 0;
      visibility: hidden;
      transition: all 0.4s ease-in;

      li {
        position: relative;

        a {
          font-size: 17px;
          font-weight: 400;
          font-family: var(--aller_lt);
          color: #e0e0e0;
          line-height: 20px;
          display: block;
          width: 100%;
          transition: all 0.3s ease-in;
          text-align: left;

          &:hover {
            color: #b6a7ff;
          }

          @media #{$media-1366} {
            font-size: 15px;
          }

          @media #{$media-820} {
            font-size: 14px;
          }

          // @media #{$media-700} {
          @media #{$media-1024} {

            text-align: center;
            padding: 7px 15px;
          }

          &.active_nav_head {
            color: #b6a7ff !important;
          }
        }
      }

      // @media #{$media-700} {
      @media #{$media-1024} {

        opacity: 0;
        visibility: hidden;
        display: none;
        transform: translateX(0%);
        position: unset;
      }

      &.submenuActive {

        // @media #{$media-700} {
        @media #{$media-1024} {

          display: flex !important;

          position: unset;
          opacity: 1 !important;
          visibility: visible !important;
          min-width: 100%;
          transform: unset;
          padding: 20px;
        }

        &.dropdown_menu {
          opacity: 1;
          visibility: visible;
          animation: dropdownAnimation 0.3s linear;

          // @media #{$media-700} {
          @media #{$media-1024} {

            opacity: 0;
            visibility: hidden;
            animation: unset;
          }
        }


      }

      @media #{$media-820} {
        padding: 15px;
      }
    }

    &:hover .dropdown_arrow img {
      transform: scaleY(-1);

      // @media #{$media-700} {
      @media #{$media-1024} {

        transform: unset;
      }
    }

    // &:hover .dropdown_menu {
    //   opacity: 1;
    //   visibility: visible;
    //   animation: dropdownAnimation 0.3s linear;

    //   // @media #{$media-700} {
    //   @media #{$media-1024} {

    //     opacity: 0;
    //     visibility: hidden;
    //     animation: unset;
    //   }
    // }




    // @media #{$media-700} {
    @media #{$media-1024} {

      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      flex-wrap: wrap;
      width: 100%;
    }
  }

  &.sticky {
    justify-content: center;
    gap: 8.7%;

    //  background: #ececec;
    //  border-bottom: 1px solid #ececec;
    li {
      a {
        color: #3b3064;
        font-weight: 600;
        // font-family: 'Aller', sans-serif;
        font-family: var(--aller_lt);



        // @media #{$media-700} {
        @media #{$media-1024} {

          color: white;
        }
      }
    }

    // @media #{$media-700} {
    @media #{$media-1024} {

      gap: 0%;
    }
  }

  &.active1 {
    visibility: visible;
    opacity: 1;
  }

  // &.sticky {
  //   // gap: 4%;
  //   // gap: 64px;
  //   gap: 8.7%;

  //   li {
  //     a {
  //       font-weight: 600;
  //     }
  //   }

  //   // @media #{$media-700} {
  //   @media #{$media-1024} {

  //     gap: 0%;
  //   }
  // }


}

.language_switch {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  background: rgba(103, 88, 182, 1);
  padding: 5px 15px;
  opacity: 0;
  color: white;
  visibility: hidden;
  transition: all 0.3s ease;

  &.language_switch_globe {
    opacity: 1;
    visibility: visible;
    background: rgba(104, 88, 182, 0);
    top: 30%;

    .icon {
      height: auto;
      // width: 21px;
      width: 25px;
      position: relative;
      z-index: 1;
      // filter: drop-shadow(0px 5px 5px #7a66db);
      filter: drop-shadow(0px 5px 5px #c4b8ff);

      >img {
        height: auto;
        width: 100% !important;
        display: block;
        object-fit: contain;
      }

      &.no_shadow {
        filter: none;
      }
    }

    // &::after {
    //   content: "";
    //   position: absolute;

    //   height: 50%;
    //   width: 20px;
    //   border-radius: 50%;
    //   top: 27%;
    //   left: 50%;
    //   transform: translateX(-50%);
    //   box-shadow: ;
    //   transition: all 0.3s ease;

    // }

    &.sticky {
      top: 50%;

      // .icon {
      //   filter: invert(1);

      //   &.no_shadow {
      //     filter: unset !important;
      //   }
      // }

      // &::after {

      //   position: unset;

      // }
    }

    @media #{$media-1024} {
      top: 40%;

    }

    @media #{$media-890} {
      right: 5px;
    }

    // @media #{$media-700} {
    @media #{$media-1024} {
      display: none;
    }
  }

  &.sticky {
    opacity: 1;
    visibility: visible;

    // @media #{$media-700} {
    @media #{$media-1024} {


      // color: rgba(103, 88, 182, 1);
    }
  }

  // @media #{$media-700} {
  @media #{$media-1024} {

    position: unset;
    right: 100px;
    background: rgba(104, 88, 182, 0);
    font-size: 18px;
    opacity: 1;
    padding: 5px 25px;

    visibility: visible;
    transform: unset;
    margin-left: auto;
    display: none;
  }
}

:global(body.rtl) .language_switch {
  right: unset !important;
  left: 20px !important;
  margin-left: unset !important;
  margin-right: auto;


}

.sticky .dropdown_arrow>img {
  filter: invert(1);

  // @media #{$media-700} {
  @media #{$media-1024} {

    filter: invert(0);
  }
}

.mob_logo {
  display: none;

  // @media #{$media-700} {
  @media #{$media-1024} {

    display: block;
    width: 80px;

    img {
      height: auto;
      width: 100%;
      display: block;
      object-fit: contain;
    }
  }
}

.hamburger {
  display: none;
  align-items: center;
  justify-content: center;
  height: auto;
  width: 27px;

  span {
    height: 2px;
    width: 30px;
    background-color: rgb(255, 255, 255);
    position: relative;
    transition: all 0.3s ease;

    &::before {
      position: absolute;
      content: "";
      height: 2px;
      width: 100%;
      bottom: -7px;
      background-color: rgb(255, 255, 255);
      transition: all 0.3s ease;
    }

    &::after {
      position: absolute;
      content: "";
      height: 2px;
      width: 100%;
      top: -7px;
      background-color: rgb(255, 255, 255);
      transition: all 0.3s ease;
    }
  }

  &.active {
    span {
      background-color: transparent;

      &::before {
        bottom: 0%;
        transform: rotate(-45deg);
        background-color: #ffffff;
      }

      &::after {
        background-color: #ffffff;
        top: 0px;
        transform: rotate(45deg);
      }
    }
  }

  &.sticky {
    @media #{$media-min-1366} {
      span {
        background: rgba(103, 88, 182, 1);

        &::before {
          background: rgba(103, 88, 182, 1);
        }

        &::after {
          background: rgba(103, 88, 182, 1);
        }
      }

    }

    &.active {
      span {
        background-color: transparent;
      }
    }
  }

  // @media #{$media-700} {
  @media #{$media-1024} {

    display: flex;
  }
}

.dropdown_icon {
  margin-left: 10px;
  margin-right: 10px;
  display: none;
  transition: all 0.3s ease;

  &.active {
    transform: scaleY(-1);
  }

  // @media #{$media-700} {
  @media #{$media-1024} {

    display: block;
    // margin-left: 5px;
    // margin-right: 5px;
    margin-left: 0;
    margin-right: 0;
    padding: 5px;
    margin-top: 5px;
    // transform: translateX(10px);
    transform: translateX(0px);

    &.active {
      // transform: translateX(10px) scaleY(-1);
      transform: translateX(0px) scaleY(-1);
    }
  }

  img {
    display: block;
  }
}

.style_mob_menu {

  // @media #{$media-700} {
  @media #{$media-1024} {

    position: absolute !important;
    // overflow: hidden;
    display: flex;
    display: none;
    width: 100%;
    align-items: center;
    flex-wrap: wrap;
    gap: 0px;
    top: 100%;
    right: 0;
    width: 100%;
    visibility: hidden;
    opacity: 0;
    // background: #565391;
    // height: 100vh;
    align-items: center;

  }

  >ul {

    // @media #{$media-700} {
    @media #{$media-1024} {

      width: 100%;
      padding: 2% 6%;
    }
  }
}

.active_nav {
  visibility: visible;
  opacity: 1;
  display: flex;

  ul {
    flex-wrap: wrap;
  }

  @media #{$media-1024} {
    background: rgb(81, 72, 135);
    margin-top: 3px;
    border: solid 1px #6758b6;
    border-top: 0;
    border-radius: 0 0 15px 15px;
  }



}

.submenu_list {
  width: unset;
  display: flex;
  align-items: center;
  flex-direction: column;

  li {
    a {
      display: block;
      font-size: 15px;
      padding: 6px 0;
      color: #b6a7ff !important;

      @media #{$media-1024} {
        font-weight: 500 !important;
        color: #fff !important;
      }

    }
  }
}

:global(body.rtl) .menu_ul_block>li .dropdown_menu {
  right: 0;
  left: inherit;
}

:global(body.rtl) .menu_ul_block>li .dropdown_menu li a {
  text-align: right;

  @media #{$media-1024} {
    text-align: center;
  }
}

@media #{$media-1024} {
  .dropdown_icon {
    margin-right: 10px;
  }
}

.mob_hide {

  @media #{$media-600} {
    display: none;
  }
}