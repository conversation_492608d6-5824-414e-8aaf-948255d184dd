import React, { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import rtl from "@/styles/rtl.module.scss";
import InnerBanner from "@/component/InnerBanner";
import ProjectSliderNewV1 from "@/component/ProjectSliderNewV1";
import comon from "@/styles/comon.module.scss";
import career from "@/styles/career.module.scss";
import HeadMarquee from "@/component/HeadMarquee";
import contactF from "@/styles/contactForm.module.scss";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import parse from 'html-react-parser';
import { fetchPageById, fetchPostList } from '@/lib/api/pageApi';
import FormCareer from "@/component/form/FormCareer";
import CountUp from "react-countup";
import { useInView } from "react-intersection-observer";
import { useRouter } from "next/router";
import SelectDropDown from "@/component/SelectDropDown";
export default function Home({ pageData, JobList }) {
	const { locale } = useRouter();
	const [postMessage, setPost] = useState('');

	const [dropdown, setDropdown] = useState("1 week");

	const handleChange = (event) => {
		setDropdown(event.target.value);
	};

	const [open, setOpen] = useState(false);
	const [job, setJob] = useState('');
	const handleOpen = (e) => {
		setOpen(true);
		setJob(e)
	};

	const handleClose = () => {
		setOpen(false);
		setJob('')
	};
	const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.2 });
	const banner = pageData.acf.banner_details;




	return (
		<>
			<HeadMarquee />
			<InnerBanner
				linkHref="/"
				linkText="Join the Movement"
				title={banner.title}
				extraClass="The Future of Work Starts Now"
				showLink={false}
				details={banner}
			/>

			{/* 
<ProjectSliderNewV1/> */}
			{pageData.acf.details.video.url && (
				<section
				// data-aos="fade-up" data-aos-duration="1000"
				>
					<video
						className={`${comon.video_block}  ${comon.video_min_height_section}  video_block video_block_home`}
						autoPlay
						muted
						loop
						playsInline={true}
						width="100%"
						height="auto"
					>
						<source src={pageData.acf.details.video.url}  type="video/mp4" />
					</video>
				</section>
			)}
			<section className={`${comon.pt_65} ${comon.res_pb_20} ${comon.pb_40}`}>
				<div className={`${comon.wrap}`}>
					<div
						className={`${comon.d_flex_wrap} ${comon.justify_space_bet} ${comon.w_100}`}
					>
						<div
							className={`${comon.title_block_main} ${comon.title_block_sub}`}
						>
							<div
								data-aos="fade-up"
								data-aos-duration="1000"
								className={`${comon.title_30} ${comon.mb_10}`}
							>
								<h3>{pageData.acf.details.list_title}</h3>
							</div>
							<div
								data-aos="fade-up"
								data-aos-duration="1000"
								className={`${comon.text_collor}`}
							>
								<p><span className="base_font">{JobList?.length || 0} </span>
								{locale=='ar'? 'المناصب المتاحة' : 'positions available'}
								</p>
							</div>
						</div>
						<span
							onClick={() => { handleOpen('general') }}
							data-aos="fade-up"
							data-aos-duration="1000"
							className={`${comon.buttion} ${comon.but_fill} ${comon.but_h_02}`}
						// href={"#"}
						>
							{pageData.acf.details.button_text}
						</span>
					</div>
				</div>
			</section>

			<section className={`${comon.pb_60}`}>
				<div className={`${comon.wrap}`}>
					<ul className={`${career.career_ul}`}>
						{JobList.map((job, index) => (
							<li
								data-aos="fade-up"
								data-aos-duration="1000"
								key={index}
								className={`${comon.pt_45} ${comon.pb_45}`}
							>
								<div
									className={`${career.career_list_left} ${rtl.career_list_left}`}
								>
									<h5>
										<span>{job.title.rendered}</span>
										<ul>
											<li>{job.acf.type}</li>
											<li>
												<Image
													src={"/images/location_icn.svg"}
													width={9.1}
													height={13}
													quality={100}
													alt="Location Icon"
												/>
												{job.acf.location}
											</li>
										</ul>
									</h5>
									<div className={`${comon.text_collor} ${comon.pt_15}`}>
										<> {parse(job.acf.job_description)} </>
									</div>
								</div>
								<div className={`${career.career_list_right}`}>
									<span
										onClick={() => handleOpen(job.title.rendered)}
										className={`${comon.buttion}  ${career.buttion} ${comon.but_blue}`}
										href={"#"}
									>
										{locale == "ar" ? "تقدم الآن" : "Apply Now"}
									</span>
								</div>
							</li>
						))}
					</ul>
				</div>
			</section>

			<section
				style={{ backgroundColor: "#E1E2E1" }}
				className={`${comon.pt_45} ${comon.pb_45}`}
			>
				<div className={`${comon.wrap} ${comon.d_flex_wrap}`}>
					<div className={`${career.bot_left_block} ${rtl.bot_left_block}`}>
						<ul>
							{/* <li data-aos="fade-up" data-aos-duration="1000">
								<Image
									src={pageData.acf.overview.logo.url}
									width={83}
									height={83}
									quality={100}
									alt="Great Icon"
								/>
							</li> */}
							<li data-aos="fade-up" data-aos-duration="1000">
								<h3>
									{parse(pageData.acf.overview.title)}
								</h3>
							</li>
						</ul>
					</div>
					<div
						ref={ref} // Attach ref to track visibility
						className={`${career.bot_right_block} ${comon.text_collor}`}>
						<ul>
							{pageData.acf.overview.counter.map((stat, index) => (
								<li data-aos="fade-up" data-aos-duration="1000" key={index}>
									<h3  className="base_font">
									{stat.prefix && stat.prefix}
										<CountUp
											start={0}
											end={stat.value}
											duration={4}
											startOnMount={false}
											redraw={true}
											delay={0}
											decimals={1}
											play={inView}
										>
											{({ countUpRef }) => (
												<span ref={countUpRef} className="base_font" />
											)}
										</CountUp>
										{stat.postfix && stat.postfix}
									</h3>
									<p >{stat.text_}</p>
								</li>

							))}

						</ul>
					</div>
				</div>
			</section>

			<div>
				<Modal
					open={open}
					onClose={handleClose}
					aria-labelledby="modal-modal-title"
					aria-describedby="modal-modal-description"
					data-lenis-prevent="true"
				>
					<Box
						// sx={style}
						className={`${contactF.popup_container} ${rtl.popup_container}`}
					>
						<button className={`${contactF.popup_close} ${rtl.popup_close}`} onClick={handleClose}>
							<Image
								src={"/images/close_btn.svg"}
								height={30}
								width={30}
								alt=" "
							/>
						</button>
						<div className={`${contactF.popup_section} ${rtl.popup_section}`}>
							<div className={`${contactF.head_sec}  ${rtl.head_sec}`}>
								<h3>{pageData.acf.form_details.title}</h3>
								<p>{parse(pageData.acf.form_details.description)}</p>
							</div>

							<FormCareer listName={JobList} jobTitle={job} />
						</div>
					</Box>
				</Modal>
			</div >
		</>
	);
}



export async function getStaticProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	//const langCode = "en"
	const slug = null;
	try {
		const pageData = await fetchPageById("career", langCode);
		const JobList = await fetchPostList('career', 3, langCode, slug);
		return {
			props: {
				pageData,
				JobList
			},
			revalidate: 10,
		};
	} catch (error) {
		console.error('Error fetching data:', error);
		return {
			props: {
				pageData: null,
				JobList: null
			},
		};
	}
}