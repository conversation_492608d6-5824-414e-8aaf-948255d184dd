"use client"; // Ensures the component is client-side rendered

import * as React from "react";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";

import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { Box } from "@mui/material";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/scrollbar";
import { Pagination } from "swiper/modules";
import { useRouter } from "next/router";
// Import Swiper styles
import "swiper/css/pagination";
import { fetchPageById, fetchPostList } from '@/lib/api/pageApi';
import HeadMarquee from "@/component/HeadMarquee";
import style from "@/styles/news.module.scss";
import parse from 'html-react-parser';
import { useState } from "react";
const news = ({ pageData, NewsList }) => {
    const router = useRouter();
    const [value, setValue] = React.useState("0");
    const { locale } = useRouter();
    const handleChange = (event, newValue) => {
        setValue(newValue);
    };
    const banner = pageData.acf.banner_details;
    const limitWords = (htmlString, wordLimit) => {
        const textOnly = htmlString.replace(/<\/?[^>]+(>|$)/g, '');
        const words = textOnly.split(' ');
        const trimmed = words.slice(0, wordLimit).join(' ');
        return parse(trimmed + (words.length > wordLimit ? '...' : ''));
    };

    const monthNames = {
        en: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
        ar: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    };
    const [viewMore, setViewMore] = useState(false);

    const ViewMoreHandler = () => {
        setViewMore(true);
    };
    return (
        <div>
            <HeadMarquee />

            <InnerBanner
                linkHref="/"
                linkText="Join the Movement"
                title={banner.title}
                extraClass="The Future of Work Starts Now"
                showLink={false}
                details={banner}
            />

            <section className={`  ${comon.pb_50}`}>
                <div className={`custom_tabs ${comon.wrap}`}>
                    <Box sx={{ width: "100%", typography: "body1" }}>
                        <TabContext value={value}>
                            <Box sx={{ borderBottom: 1, overflowY: 'auto', borderColor: "divider" }}>
                                <TabList
                                    onChange={handleChange}
                                    variant="scrollable"
                                    scrollButtons
                                    allowScrollButtonsMobile

                                    aria-label="lab API tabs example"
                                >
                                    {pageData.acf.category && pageData.acf.category.map((item, index) => {
                                        return (
                                            <Tab label={item.name} value={index.toString()} key={index} />
                                        )
                                    })}

                                </TabList>
                            </Box>
                            {pageData.acf.category && pageData.acf.category.map((cat, index) => {
                                const filteredNews = NewsList.filter(news =>
                                    news["news-category"] && news["news-category"].includes(cat.term_id)
                                );
                                return (
                                    <TabPanel value={index.toString()}>
                                        {" "}
                                        <div>
                                            <div className={`${style.news_section} ${comon.pt_40}`}>
                                                <Swiper
                                                    className="mySwiper news_swiper news_swiper_max_height "
                                                    pagination={{ clickable: true }}
                                                    modules={[Pagination]}
                                                    autoHeight={true}
                                                    dir={router.locale == "ar" || router.locale == "rtl" ? "rtl" : "ltr"}
                                                >
                                                    {filteredNews.slice(0, 2).map((news, index1) => {
                                                        const fixedDate = new Date(news.date);
                                                        const day = fixedDate.getDate();
                                                        const year = fixedDate.getFullYear();
                                                        const month = monthNames[locale === 'ar' ? 'ar' : 'en'][fixedDate.getMonth()];

                                                        // const formattedFixedDate = `${day} ${month} ${year}`;
                                                        const formattedFixedDate = (
                                                            <div className="news_date_section">
                                                                <span className="base_font ">{day}</span> {month} <span className="base_font">{year}</span>
                                                            </div>
                                                        );
                                                        // console.log("gtsbajbdshucb", news._embedded["wp:featuredmedia"][0].source_url)
                                                        return (
                                                            <SwiperSlide key={index1}>
                                                                {/* <Link className={style.swiper_content} href={`news/${news.slug}`}> */}
                                                                <div className={style.swiper_content} >
                                                                    <div className={`${style.swiper_section} ${rtl.swiper_section} `}>
                                                                        <ul
                                                                            className={`${comon.mb_25} ${style.breadcrumb} ${rtl.breadcrumb} ${rtl.px_unset}`}
                                                                        >
                                                                            {news?.news_tags_info?.slice(0, 2).map((item, index) => {
                                                                                return (
                                                                                    <li key={index}>
                                                                                        <span>{item.name}</span>
                                                                                    </li>
                                                                                )
                                                                            })}
                                                                            {/* <li>
                                                                                <span>{news.author_name}</span>
                                                                            </li> */}
                                                                            <li>
                                                                                <span className="base_font  ">{formattedFixedDate}</span>
                                                                            </li>
                                                                        </ul>


                                                                        <h4 className="trimTitle">
                                                                            {/* {limitWords(news.title.rendered, 25)}  */}
                                                                            {news.title.rendered && parse(news.title.rendered)}
                                                                        </h4>
                                                                        <>
                                                                            <div className="newsL">
                                                                                {news.excerpt.rendered && parse(news.excerpt.rendered)}
                                                                            </div>
                                                                        </>


                                                                        {/* <Link className={style.link}  href={`news/${news.slug}`}> */}
                                                                        <Link className={comon.link} href={`news/${news.slug}`}>
                                                                            {locale === "ar" ? "اقرأ المزيد" : "Read More"}
                                                                        </Link>


                                                                    </div>

                                                                    <style jsx global>{`
                                                                    .trimTitle {
                                                                        display: -webkit-box;
                                                                        -webkit-line-clamp: 12;
                                                                        -webkit-box-orient: vertical;
                                                                        overflow: hidden;
                                                                        text-overflow: ellipsis;
                                                                    }

                                                                    .newsL > p {
                                                                        display: -webkit-box;
                                                                        -webkit-line-clamp: 3;
                                                                        -webkit-box-orient: vertical;
                                                                        overflow: hidden;
                                                                        text-overflow: ellipsis;
                                                                    }
                                                                    `}</style>


                                                                    <div className={`${style.img_section} ${style.img_cover_sec}`}>
                                                                        <Image
                                                                            src={news._embedded["wp:featuredmedia"][0].source_url}
                                                                            height={500}
                                                                            width={500}
                                                                            alt=""
                                                                        />
                                                                    </div>
                                                                </div>
                                                                {/* </Link> */}
                                                            </SwiperSlide>
                                                        );

                                                    })}


                                                </Swiper>
                                            </div>
                                            <div className={`${comon.pt_70} ${style.list_section}`}>
                                                <ul className={`${style.ul_section} ${rtl.ul_section}`}>
                                                    {/* {filteredNews.slice(0, 5).map((news, index2) => { */}
                                                    {(viewMore ? filteredNews : filteredNews.slice(0, 5)).map((news, index2) => {
                                                        const fixedDate = new Date(news.date);
                                                        const day = fixedDate.getDate();
                                                        const year = fixedDate.getFullYear();
                                                        const month = monthNames[locale === 'ar' ? 'ar' : 'en'][fixedDate.getMonth()];

                                                        // const formattedFixedDate = `${day} ${month} ${year}`;
                                                        const formattedFixedDate = (
                                                            <div className="news_date_section">
                                                                <span className="base_font   ">{day}</span> {month} <span className="base_font">{year}</span>
                                                            </div>
                                                        );
                                                        return (

                                                            <li key={index2}>
                                                                <ul className={`${style.ul_content} ${rtl.ul_content}`}>
                                                                    <li>
                                                                        <span >{formattedFixedDate}</span>
                                                                    </li>
                                                                    <li>
                                                                        <Link href={`news/${news.slug}`}>
                                                                            <p>
                                                                                {parse(news.title.rendered)}
                                                                            </p>
                                                                        </Link>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        );

                                                    })}

                                                </ul>
                                                {!viewMore &&
                                                    <div onClick={ViewMoreHandler} className={` ${comon.link}  ${rtl.arb_font} ${comon.mt_30} `}  >
                                                        {locale === "ar" ? "عرض الكل" : "View More"}
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </TabPanel>

                                )
                            })}


                        </TabContext>
                    </Box>
                </div>
            </section >
        </div >
    );
};

export default news;


export async function getStaticProps({ locale }) {
    const langCode = locale === "ar" ? "ar" : "en";
    ///const langCode = "en"
    const slug = null;
    try {
        const pageData = await fetchPageById("news", locale);
        const NewsList = await fetchPostList('news', 100, locale, slug);
        return {
            props: {
                pageData,
                NewsList
            },
            revalidate: 10,
        };
    } catch (error) {
        console.error('Error fetching data:', error);
        return {
            props: {
                pageData: null,
            },
        };
    }
}