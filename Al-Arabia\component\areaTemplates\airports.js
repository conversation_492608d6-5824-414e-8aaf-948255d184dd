import HeadMarquee from "@/component/HeadMarquee";
import React, { useState, useEffect } from "react";
import InnerBanner from "@/component/InnerBanner";
import comon from "@/styles/comon.module.scss";
import about from "@/styles/about.module.scss";
import rtl from "@/styles/rtl.module.scss";
import Link from "next/link";
import Image from "next/image";
import CountUp from "@/component/CountUp";
import StartCampaign from "@/component/StartCampaign";
import Head from "next/head";
import AirplaneAnim from "@/component/AirplaneAnim";
import { fetchPostList } from "@/lib/api/pageApi";
import SliderBannerText from "@/component/SliderBannerText";
import { useRouter } from 'next/router';
import parse from 'html-react-parser';
import { fetchByPost } from "@/lib/api/pageApi";

import { gsap } from "gsap";
import { MotionPathPlugin } from "gsap/dist/MotionPathPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import AirAnimPage from "@/component/AirAnimPage";
import AirAnimPageNew from "@/component/AirAnimPageNew";
gsap.registerPlugin(MotionPathPlugin, ScrollTrigger);

const airports = ({ pageData }) => {
    // const [tabSelect, setTabSelect] = useState();

    // const scrollToSection = (sectionId) => {
    //     setTabSelect(sectionId);
    //     const section = document.getElementById(sectionId);
    //     if (section) {
    //         section.scrollIntoView({ behavior: "smooth" });
    //     }
    // };
    const sections = ["section1", "section2", "section3"];
    const [activeSection, setActiveSection] = useState(null);

    const { locale, asPath } = useRouter();

    const lastSegment = asPath.split("/").filter(Boolean).pop();
    const [airportList, setAirportList] = useState(null);
    const slug = null;
    let delayCounter = 2;
    let delayCounter1 = 1;
    // Sections list for tracking
    // const sections = [];
    // for (let i = 1; i <= 10; i++) {
    //     sections.push(`section${i}`);
    // }
    //console.log(sections);

    useEffect(() => {
        if (!airportList) return;

        const sectionsDynamic = ["section1"];
        let dynamicCounter = 1;

        airportList
            .filter((section) => section.parent_slug === lastSegment && section.template !== "coming-soon.php")
            .forEach(() => {
                dynamicCounter++;
                sectionsDynamic.push(`section${dynamicCounter}`);
            });

        const observerOptions = {
            root: null,
            threshold: 0.6,
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    setActiveSection(entry.target.id);
                }
            });
        }, observerOptions);

        sectionsDynamic.forEach((sectionId) => {
            const section = document.getElementById(sectionId);
            if (section) observer.observe(section);
        });

        return () => observer.disconnect();
    }, [airportList, lastSegment]);


    const scrollToSection = (sectionId) => {
        setActiveSection(sectionId); // Set active section state
        const element = document.getElementById(sectionId);

        if (element) {
            const offset = 120; // Adjust this based on your header height
            const elementPosition =
                element.getBoundingClientRect().top + window.scrollY;

            window.scrollTo({
                top: elementPosition - offset,
                behavior: "smooth",
            });
        }
    };



    useEffect(() => {
        const fetchOptions = async () => {
            try {
                const res = await fetch(
                    `${process.env.NEXT_PUBLIC_API_BASE_URL}/areas?acf_format=standard&per_page=100&lang=${locale}&_embed`
                );
                const data = await res.json();
                setAirportList(data);
            } catch (error) {
                console.error("Error fetching options:", error);
            }
        };

        fetchOptions();
    }, [locale]);

    if (!airportList) {
        return <div style={{ minHeight: '70vh' }}> </div>
    }


    return (
        <div>
            <HeadMarquee />
            {pageData.acf.banner_details.description_field ? (

                <SliderBannerText
                    title={pageData.acf.banner_details.title}
                    paragraph={pageData.acf.banner_details.description_}
                    details={pageData.acf.banner_details}
                    className={'width_870'}
                    bgColor={true}
                    extra_height_class={true}
                    full_height_banner={true}
                />
            ) : (
                <InnerBanner showLink={false} title={pageData.acf.banner_details.title} details={pageData.acf.banner_details} />
            )}

            <section className={comon.d_flex_wrap}>
                <div
                    className={`${comon.d_flex_wrap} ${comon.p_relative} ${comon.w_100}`}
                >
                    <div className={about.left_block}>
                        <div className={`${about.left_block_block} ${about.sticky}`}>
                            <ul
                                data-aos="fade-in"
                                data-aos-duration="1000"
                                className={about.right_block_ul}
                            >
                                <li
                                    data-aos-delay={200}
                                    className={about.max_width}
                                >
                                    <span
                                        onClick={() => scrollToSection("section1")}
                                        className={activeSection === "section1" ? about.active : ""}
                                    >
                                        {parse(pageData.acf.overview_details.sidebar_title)}
                                    </span>
                                </li>
                                {airportList &&
                                    airportList
                                        .filter((section) => section.parent_slug === lastSegment)
                                        .sort((a, b) => (a.template === "coming-soon.php" ? 1 : b.template === "coming-soon.php" ? -1 : 0))
                                        .map((section, index) => {
                                            if (section.parent_slug === lastSegment) {
                                                const counterId = delayCounter;
                                                delayCounter++;
                                                const delay = 200 * counterId;

                                                if (section.template == "coming-soon.php") {
                                                    return (
                                                        <li
                                                            key={index}
                                                            data-aos="fade-in"
                                                            data-aos-delay={delay}
                                                            className={about.max_width}
                                                        >
                                                            {/* <Link href={section.slug}> */}
                                                            <a >
                                                                {section.title.rendered}
                                                            </a>
                                                        </li>
                                                    )
                                                }
                                                //sections.push(`section${counterId}`); 
                                                return (
                                                    <li
                                                        key={index}
                                                        data-aos="fade-in"
                                                        data-aos-delay={delay}
                                                        className={about.max_width}
                                                        id={delayCounter}
                                                    >
                                                        <span
                                                            onClick={() => scrollToSection(`section${counterId}`)}
                                                            className={activeSection === `section${counterId}` ? about.active : ""}
                                                        >
                                                            {section.title.rendered}
                                                        </span>
                                                    </li>
                                                );

                                            }
                                            return null;
                                        })}


                            </ul>
                        </div>
                    </div>

                    <div
                        className={`${about.right_block} `}
                    >
                        <div
                            className={about.airplane_moving_section}
                            id='motion_start'>

                            <div
                                className={`${comon.w_100} ${comon.pt_50} ${comon.pb_50}`}
                                id="section1"
                            >
                                <ul
                                    className={`${about.two_cl_ul} ${about.airplane_section}  ${about.padding_space}`}
                                >
                                    <li
                                        data-aos="fade-in"
                                        data-aos-duration="1000"
                                        className={`${comon.w_50} ${about.mission_vission_block} ${about.bringing_block}`}
                                    >
                                        <h3>
                                            {parse(pageData.acf.overview_details.overview_content.title)}
                                        </h3>
                                        <>
                                            {parse(pageData.acf.overview_details.overview_content.description)}
                                        </>
                                    </li>
                                    <li
                                        data-aos="fade-in"
                                        data-aos-duration="1000"
                                        className={`${comon.w_50} ${about.mission_vission_block} ${about.airplane_block}`}
                                    >
                                        {/* <p>
                                        To cement our position as the leading Saudi company in
                                        out-of-home media and to become the catalyst of national
                                        economy -media sector- by expanding our leadership in the
                                        Middle East region.
                                    </p> */}
{/* 
                                        <div className={`  ${about.airplane_img}`}>
                                            <Image src={'/images/airplane-img.svg'} height={250} width={400} alt="" />
                                            <Image
                                            src={"/images/airplane-img-anim1.svg"}
                                            height={250}
                                            width={400}
                                            alt=""
                                        />

                                        </div> */}
                                    </li>
                                </ul>
                            </div>
                            {/* <AirAnimPage /> */}
                            {pageData && (
                                <AirAnimPageNew />
                            )}
                            <CountUp counterData={pageData.acf.overview_details.overview_content.counter} isMaxWidthDisable={true} />

                            <ul
                                className={`${comon.airport_img_text_section}  ${rtl.airport_img_text_section}  ${comon.pb_65}  `}
                            >
                                {airportList &&
                                    airportList
                                        .filter((section) => section.parent_slug === lastSegment)
                                        .sort((a, b) => (a.template === "coming-soon.php" ? 1 : b.template === "coming-soon.php" ? -1 : 0))
                                        .map((section, index) => {
                                            if (section.parent_slug == lastSegment) {
                                                delayCounter1++;
                                                if (section.template !== "coming-soon.php") {
                                                    return (
                                                        <li data-aos="fade-in" data-aos-duration="1000" id={`section${delayCounter1}`} key={index}>
                                                            <div
                                                                className={comon.img_section}
                                                                data-aos="fade-in"
                                                                data-aos-duration="1000"
                                                            >
                                                                <Image
                                                                    src={section.acf.for_listing_page.image.url}
                                                                    alt=""
                                                                    width={500}
                                                                    height={500}
                                                                    quality={100}
                                                                />
                                                            </div>
                                                            <div
                                                                className={`${comon.text_section} ${rtl.text_section}`}
                                                            >
                                                                <h3>{parse(section.title.rendered)}</h3>
                                                                <>
                                                                    {parse(section.acf.for_listing_page.description)}
                                                                </>
                                                                <div>
                                                                    <Link href={`${section.slug}`}>
                                                                        {locale == 'ar' ? 'اعرف أكثر' : 'Learn More'}
                                                                    </Link>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    )

                                                }
                                            }
                                        })}

                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <StartCampaign />
        </div>
    );
};

export default airports;

